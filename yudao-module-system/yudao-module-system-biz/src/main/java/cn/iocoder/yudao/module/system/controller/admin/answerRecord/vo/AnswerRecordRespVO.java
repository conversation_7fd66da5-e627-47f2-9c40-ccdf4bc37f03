package cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户回答记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AnswerRecordRespVO {

    @Schema(description = "主键id,即记录Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19609")
    @ExcelProperty("主键id,即记录Id")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6060")
    @ExcelProperty("用户Id")
    private Integer userId;

    @Schema(description = "上一次回答的问题Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20408")
    @ExcelProperty("上一次回答的问题Id")
    private Integer lastAnswerQuestionId;

    @Schema(description = "用户第多少次答题")
    @ExcelProperty("用户第多少次答题")
    private Integer answerNo;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 类型 1体验版 2专业版
     */
    private Integer type;

    private Long reportId;

    @Schema(description = "报告名称")
    @ExcelProperty("报告名称")
    private String reportName;

    @Schema(description = "已生成报告数量")
    @ExcelProperty("已生成报告数量")
    private Integer reportCount;

    @Schema(description = "是否推荐")
    @ExcelProperty("是否推荐")
    private Boolean recommend;

}