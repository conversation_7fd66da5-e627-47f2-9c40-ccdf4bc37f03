package cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 用户回答记录新增/修改 Request VO")
@Data
public class AnswerRecordSaveReqVO {

    @Schema(description = "主键id,即记录Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19609")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6060")
    @NotNull(message = "用户Id不能为空")
    private Integer userId;

    @Schema(description = "上一次回答的问题Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20408")
    @NotNull(message = "上一次回答的问题Id不能为空")
    private Integer lastAnswerQuestionId;

    @Schema(description = "用户第多少次答题")
    private Integer answerNo;

    @Schema(description = "状态", example = "2")
    private Integer status;
    /**
     * 类型 1体验版 2专业版
     */
    private Integer type;

    @Schema(description = "报告名称")
    private String reportName;

    @Schema(description = "已生成报告数量")
    private Integer reportCount;

    @Schema(description = "是否推荐")
    private Boolean recommend;
}