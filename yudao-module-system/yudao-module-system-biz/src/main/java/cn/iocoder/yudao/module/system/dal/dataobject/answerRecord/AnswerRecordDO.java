package cn.iocoder.yudao.module.system.dal.dataobject.answerRecord;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户回答记录 DO
 *
 * <AUTHOR>
 */
@TableName("tb_answer_record")
@KeySequence("tb_answer_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnswerRecordDO extends BaseDO {

    /**
     * 主键id,即记录Id
     */
    @TableId
    private Integer id;
    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 上一次回答的问题Id
     */
    private Integer lastAnswerQuestionId;
    /**
     * 用户第多少次答题
     */
    private Integer answerNo;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 类型 1体验版 2专业版
     */
    private Integer type;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 已生成报告数量
     */
    private Integer reportCount;

    /**
     * 版本号，用于乐观锁控制
     */
    @Version
    private Integer version;

    /**
     * 是否推荐
     */
    private Boolean recommend;

}